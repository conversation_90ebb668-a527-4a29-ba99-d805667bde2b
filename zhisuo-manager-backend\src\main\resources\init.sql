-- 智索管理系统数据库初始化脚本

-- 创建管理员表
CREATE TABLE IF NOT EXISTS `admin` (
  `admin_id` varchar(32) NOT NULL COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password_hash` varchar(128) NOT NULL COMMENT '密码哈希值',
  `password_salt` varchar(32) DEFAULT NULL COMMENT '密码盐值',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(15) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `role` varchar(20) NOT NULL DEFAULT 'manager' COMMENT '角色(admin:超级管理员, manager:普通管理员)',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(1:正常, 0:禁用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 插入默认管理员账户
-- 用户名: admin, 密码: admin123
INSERT INTO `admin` (`admin_id`, `username`, `password_hash`, `real_name`, `email`, `role`, `status`, `create_time`, `update_time`)
VALUES ('admin001', 'admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', '<EMAIL>', 'admin', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE `password_hash` = VALUES(`password_hash`);

-- 创建操作日志表
CREATE TABLE IF NOT EXISTS `operation_log` (
  `log_id` varchar(32) NOT NULL COMMENT '日志ID',
  `admin_id` varchar(32) NOT NULL COMMENT '管理员ID',
  `admin_name` varchar(50) NOT NULL COMMENT '管理员用户名',
  `operation` varchar(100) NOT NULL COMMENT '操作类型',
  `method` varchar(10) NOT NULL COMMENT '请求方法',
  `url` varchar(500) NOT NULL COMMENT '请求URL',
  `ip` varchar(50) DEFAULT NULL COMMENT '操作IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_params` text COMMENT '请求参数',
  `response_result` text COMMENT '响应结果',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(1:成功, 0:失败)',
  `error_msg` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `execute_time` int(11) DEFAULT NULL COMMENT '执行时间(毫秒)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_operation` (`operation`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS `system_config` (
  `config_id` varchar(32) NOT NULL COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_desc` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型(string, number, boolean, json)',
  `is_system` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否系统配置(1:是, 0:否)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入默认系统配置
INSERT INTO `system_config` (`config_id`, `config_key`, `config_value`, `config_desc`, `config_type`, `is_system`) VALUES
('config001', 'system.name', '智索管理系统', '系统名称', 'string', 1),
('config002', 'system.version', '1.0.0', '系统版本', 'string', 1),
('config003', 'system.copyright', '© 2025 智索科技', '版权信息', 'string', 1),
('config004', 'system.logo', '/static/images/logo.png', '系统Logo', 'string', 0),
('config005', 'system.favicon', '/static/images/favicon.ico', '网站图标', 'string', 0)
ON DUPLICATE KEY UPDATE `config_value` = VALUES(`config_value`);
